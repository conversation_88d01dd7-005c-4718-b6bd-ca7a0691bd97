import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useTeamsToken } from '@avanade-teams/auth';
import SimpleModal, { ISimpleModalProps } from './SimpleModal';
import useUserChatsAndChannelsAccessor from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';

// モック設定
jest.mock('@avanade-teams/auth');
jest.mock('../../../../hooks/accessors/useUserChatsAndChannelsAccessor');

const mockUseTeamsToken = useTeamsToken as jest.MockedFunction<typeof useTeamsToken>;
const mockUseUserChatsAndChannelsAccessor = useUserChatsAndChannelsAccessor as jest.MockedFunction<typeof useUserChatsAndChannelsAccessor>;

describe('SimpleModal', () => {
  const defaultProps: ISimpleModalProps = {
    open: true,
    title: 'テストモーダル',
    onClose: jest.fn(),
    onChatItemBookmarkToggle: jest.fn(),
  };

  const mockTokenProvider = jest.fn().mockResolvedValue('mock-token');
  const mockFetchUserChatsAndChannels = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // useTeamsTokenのモック設定
    mockUseTeamsToken.mockReturnValue({
      callbacks: {
        get: mockTokenProvider,
      },
      error: null,
      teamsToken: 'mock-token',
    } as any);

    // useUserChatsAndChannelsAccessorのモック設定
    mockUseUserChatsAndChannelsAccessor.mockReturnValue({
      fetchUserChatsAndChannels: mockFetchUserChatsAndChannels,
      isLoading: false,
      error: null,
    });
  });

  describe('基本的なレンダリングテスト', () => {
    it('モーダルが正常にレンダリングされる', () => {
      render(<SimpleModal {...defaultProps} />);
      
      expect(screen.getByText('テストモーダル')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('チャットIDを入力')).toBeInTheDocument();
      expect(screen.getByText('★をつけると検索対象になります。')).toBeInTheDocument();
    });

    it('openがfalseの場合、モーダルが非表示になる', () => {
      const { container } = render(<SimpleModal {...defaultProps} open={false} />);
      
      expect(container.firstChild).not.toHaveClass('is-open');
    });

    it('openがtrueの場合、モーダルが表示される', () => {
      const { container } = render(<SimpleModal {...defaultProps} open={true} />);
      
      expect(container.firstChild).toHaveClass('is-open');
    });
  });

  describe('データ取得テスト', () => {
    it('モーダルが開かれた時にチャットとチャネルを取得する', async () => {
      const mockChatItems = [
        {
          id: 'chat1',
          name: 'テストチャット1',
          type: 'チャット' as const,
          isBookmarked: false,
        },
        {
          id: 'channel1',
          name: 'テストチーム - テストチャネル1',
          type: 'チャネル' as const,
          isBookmarked: true,
        },
      ];

      mockFetchUserChatsAndChannels.mockResolvedValue(mockChatItems);

      render(<SimpleModal {...defaultProps} />);

      await waitFor(() => {
        expect(mockFetchUserChatsAndChannels).toHaveBeenCalledTimes(1);
      });
    });

    it('ローディング状態が正しく表示される', () => {
      mockUseUserChatsAndChannelsAccessor.mockReturnValue({
        fetchUserChatsAndChannels: mockFetchUserChatsAndChannels,
        isLoading: true,
        error: null,
      });

      render(<SimpleModal {...defaultProps} />);
      
      expect(screen.getByText('チャットとチャネルを読み込み中...')).toBeInTheDocument();
    });

    it('エラー状態が正しく表示される', () => {
      mockUseUserChatsAndChannelsAccessor.mockReturnValue({
        fetchUserChatsAndChannels: mockFetchUserChatsAndChannels,
        isLoading: false,
        error: 'テストエラー',
      });

      render(<SimpleModal {...defaultProps} />);
      
      expect(screen.getByText(/エラーが発生しました:/)).toBeInTheDocument();
      expect(screen.getByText(/テストエラー/)).toBeInTheDocument();
    });
  });

  describe('検索機能テスト', () => {
    const mockChatItems = [
      {
        id: 'chat1',
        name: 'テストチャット1',
        type: 'チャット' as const,
        isBookmarked: false,
      },
      {
        id: 'chat2',
        name: 'プロジェクトチャット',
        type: 'チャット' as const,
        isBookmarked: false,
      },
      {
        id: 'channel1',
        name: 'テストチーム - 一般',
        type: 'チャネル' as const,
        isBookmarked: true,
      },
    ];

    beforeEach(() => {
      mockFetchUserChatsAndChannels.mockResolvedValue(mockChatItems);
    });

    it('検索入力でチャット名によるフィルタリングが動作する', async () => {
      render(<SimpleModal {...defaultProps} />);

      // データが読み込まれるまで待機
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('チャットIDを入力');
      fireEvent.change(searchInput, { target: { value: 'テスト' } });

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
        expect(screen.getByText('テストチーム - 一般')).toBeInTheDocument();
        expect(screen.queryByText('プロジェクトチャット')).not.toBeInTheDocument();
      });
    });

    it('検索入力でチャットIDによるフィルタリングが動作する', async () => {
      render(<SimpleModal {...defaultProps} />);

      // データが読み込まれるまで待機
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('チャットIDを入力');
      fireEvent.change(searchInput, { target: { value: 'chat1' } });

      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
        expect(screen.queryByText('プロジェクトチャット')).not.toBeInTheDocument();
        expect(screen.queryByText('テストチーム - 一般')).not.toBeInTheDocument();
      });
    });

    it('検索結果が0件の場合、適切なメッセージが表示される', async () => {
      render(<SimpleModal {...defaultProps} />);

      // データが読み込まれるまで待機
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('チャットIDを入力');
      fireEvent.change(searchInput, { target: { value: '存在しないチャット' } });

      await waitFor(() => {
        expect(screen.getByText('該当するチャットまたはチャネルが見つかりませんでした。')).toBeInTheDocument();
      });
    });

    it('検索入力をクリアすると全てのアイテムが表示される', async () => {
      render(<SimpleModal {...defaultProps} />);

      // データが読み込まれるまで待機
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('チャットIDを入力');
      
      // 検索してフィルタリング
      fireEvent.change(searchInput, { target: { value: 'テスト' } });
      await waitFor(() => {
        expect(screen.queryByText('プロジェクトチャット')).not.toBeInTheDocument();
      });

      // 検索をクリア
      fireEvent.change(searchInput, { target: { value: '' } });
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
        expect(screen.getByText('プロジェクトチャット')).toBeInTheDocument();
        expect(screen.getByText('テストチーム - 一般')).toBeInTheDocument();
      });
    });
  });

  describe('ブックマーク機能テスト', () => {
    it('ブックマークの切り替えが正しく動作する', async () => {
      const mockOnBookmarkToggle = jest.fn();
      const mockChatItems = [
        {
          id: 'chat1',
          name: 'テストチャット1',
          type: 'チャット' as const,
          isBookmarked: false,
        },
      ];

      mockFetchUserChatsAndChannels.mockResolvedValue(mockChatItems);

      render(<SimpleModal {...defaultProps} onChatItemBookmarkToggle={mockOnBookmarkToggle} />);

      // データが読み込まれるまで待機
      await waitFor(() => {
        expect(screen.getByText('テストチャット1')).toBeInTheDocument();
      });

      // ブックマークボタンをクリック
      const bookmarkButton = screen.getByRole('button');
      fireEvent.click(bookmarkButton);

      expect(mockOnBookmarkToggle).toHaveBeenCalledWith('chat1', true);
    });
  });

  describe('閉じるボタンテスト', () => {
    it('閉じるボタンをクリックするとonCloseが呼ばれる', () => {
      const mockOnClose = jest.fn();
      render(<SimpleModal {...defaultProps} onClose={mockOnClose} />);

      const closeButtons = screen.getAllByRole('button');
      const closeButton = closeButtons.find(button => button.getAttribute('aria-label') === 'Close');
      
      if (closeButton) {
        fireEvent.click(closeButton);
        expect(mockOnClose).toHaveBeenCalledTimes(1);
      }
    });
  });
});
