import * as React from 'react';
import {
  <PERSON><PERSON>, Header, Input,
} from '@fluentui/react-northstar';
import { CloseIcon } from '@fluentui/react-icons-northstar';
import { useTeamsToken } from '@avanade-teams/auth';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import BookmarkStar from '../../../commons/atoms/bookmark-star/BookmarkStar';
import useUserChatsAndChannelsAccessor, { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';

// CSS
import './SimpleModal.scss';

// チャットアイテムの型定義（後方互換性のため残す）
export interface IChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
  isBookmarked: boolean;
}

export interface ISimpleModalProps {
  className?: string;
  open?: boolean;
  title?: string;
  onClose?: () => void;
  userId?: string; // ユーザーIDを追加
  chatItems?: IChatItem[]; // 後方互換性のため残す
  onChatItemBookmarkToggle?: (id: string, isBookmarked: boolean) => void;
}

/**
 * SimpleModal
 * @param props
 */
const SimpleModal: React.FC<ISimpleModalProps> = (props) => {
  const {
    className,
    open,
    title = 'Teams設定',
    onClose,
    userId,
    chatItems: propsChatItems,
    onChatItemBookmarkToggle,
  } = props;

  // Teams認証トークンを取得
  const { callbacks } = useTeamsToken();
  const tokenProvider = callbacks?.get;

  // APIアクセサーを初期化
  const { fetchUserChatsAndChannels, isLoading, error } = useUserChatsAndChannelsAccessor(tokenProvider);

  // 状態管理
  const [chatId, setChatId] = React.useState('');
  const [allChatItems, setAllChatItems] = React.useState<IUserChatItem[]>([]);
  const [filteredChatItems, setFilteredChatItems] = React.useState<IUserChatItem[]>([]);

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('simple-modal', className);
    const isOpen = open ? 'is-open' : '';
    return mergedClassName(isOpen, step1);
  }, [className, open]);

  // データ取得のEffect
  React.useEffect(() => {
    if (open && fetchUserChatsAndChannels) {
      fetchUserChatsAndChannels()
        .then((items) => {
          setAllChatItems(items);
          setFilteredChatItems(items);
        })
        .catch((err) => {
          console.error('Failed to fetch chats and channels:', err);
          // プロパティで渡されたアイテムがあればそれを使用
          if (propsChatItems) {
            const convertedItems: IUserChatItem[] = propsChatItems.map((item) => ({
              ...item,
            }));
            setAllChatItems(convertedItems);
            setFilteredChatItems(convertedItems);
          }
        });
    }
  }, [open, fetchUserChatsAndChannels, propsChatItems]);

  // 検索フィルタリングのEffect
  React.useEffect(() => {
    if (!chatId.trim()) {
      setFilteredChatItems(allChatItems);
    } else {
      const filtered = allChatItems.filter((item) => item.id.toLowerCase().includes(chatId.toLowerCase())
        || item.name.toLowerCase().includes(chatId.toLowerCase()));
      setFilteredChatItems(filtered);
    }
  }, [chatId, allChatItems]);

  const handleClose = React.useCallback(() => {
    if (onClose) onClose();
  }, [onClose]);

  // チャットID入力の変更ハンドラー
  const handleChatIdChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setChatId(data?.value ?? '');
    },
    [],
  );

  // ブックマーク切り替えハンドラー
  const handleBookmarkToggle = React.useCallback((itemId: string, isBookmarked: boolean) => {
    if (onChatItemBookmarkToggle) {
      onChatItemBookmarkToggle(itemId, isBookmarked);
    }
  }, [onChatItemBookmarkToggle]);

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="simple-modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="simple-modal-close-pc">
        <Button
          className="simple-modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="simple-modal-scroll-wrapper">
        <div className="simple-modal-scroll-inner">
          <div className="simple-modal-header">
            <Header content={title} as="h3" className="simple-modal-title" />
          </div>
          <div className="simple-modal-content">
            <p>★をつけると検索対象になります。</p>

            {/* チャットID入力フィールド */}
            <div className="simple-modal-chat-input">
              <Input
                placeholder="チャットIDを入力"
                value={chatId}
                onChange={handleChatIdChange}
                fluid
              />
            </div>

            {/* チャットアイテム一覧 */}
            <div className="simple-modal-chat-items">
              {isLoading && (
                <div className="simple-modal-loading">
                  <p>チャットとチャネルを読み込み中...</p>
                </div>
              )}
              {error && (
                <div className="simple-modal-error">
                  <p>
                    エラーが発生しました:
                    {error}
                  </p>
                </div>
              )}
              {!isLoading && !error && filteredChatItems.length === 0 && (
                <div className="simple-modal-no-results">
                  <p>該当するチャットまたはチャネルが見つかりませんでした。</p>
                </div>
              )}
              {!isLoading && !error && filteredChatItems.map((item) => (
                <div key={item.id} className="simple-modal-chat-item">
                  <div className="simple-modal-chat-item-content">
                    <span className="simple-modal-chat-item-label">
                      {item.type}
                      ：
                    </span>
                    <span className="simple-modal-chat-item-name">{item.name}</span>
                  </div>
                  <BookmarkStar
                    isBookmarked={item.isBookmarked}
                    onClick={(toBe) => handleBookmarkToggle(item.id, toBe)}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

SimpleModal.defaultProps = {
  className: '',
  open: false,
  title: undefined,
  onClose: undefined,
  userId: undefined,
  chatItems: undefined, // APIから取得するため、デフォルトは空
  onChatItemBookmarkToggle: undefined,
};

export default SimpleModal;
